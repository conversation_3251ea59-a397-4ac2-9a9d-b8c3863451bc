import type { FastifyRequest, FastifyReply } from 'fastify';

// Simple in-memory metrics store
class MetricsStore {
  private httpRequestsTotal: number = 0;

  incrementHttpRequests(): void {
    this.httpRequestsTotal++;
  }

  getHttpRequestsTotal(): number {
    return this.httpRequestsTotal;
  }

  reset(): void {
    this.httpRequestsTotal = 0;
  }
}

// Global metrics instance
const metrics = new MetricsStore();

/**
 * Middleware to count HTTP requests
 * This hook runs on every request to increment the counter
 */
export const metricsMiddleware = async (
  request: FastifyRequest,
  reply: FastifyReply
): Promise<void> => {
  // Skip counting requests to the metrics endpoint itself to avoid infinite counting
  if (request.url !== '/metrics') {
    metrics.incrementHttpRequests();
  }
};

/**
 * Generate Prometheus-compatible metrics output
 * Returns metrics in the standard Prometheus text format
 */
export const getMetrics = (): string => {
  const httpRequestsTotal = metrics.getHttpRequestsTotal();
  
  return [
    '# HELP http_requests_total Total number of HTTP requests',
    '# TYPE http_requests_total counter',
    `http_requests_total ${httpRequestsTotal}`,
    '', // Empty line at the end as per Prometheus format
  ].join('\n');
};

/**
 * Reset metrics (useful for testing)
 */
export const resetMetrics = (): void => {
  metrics.reset();
};

/**
 * Get current metrics values (useful for testing)
 */
export const getCurrentMetrics = () => {
  return {
    httpRequestsTotal: metrics.getHttpRequestsTotal(),
  };
};
