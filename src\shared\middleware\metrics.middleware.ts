import type { FastifyRequest, FastifyReply } from 'fastify';

// Simple in-memory metrics store
class MetricsStore {
  private httpRequestsTotal: number = 0;
  private httpRequestsByMethod: Map<string, number> = new Map();
  private httpRequestsByStatus: Map<number, number> = new Map();
  private httpRequestsByRoute: Map<string, number> = new Map();
  private httpRequestDurationSum: number = 0;
  private httpRequestDurationCount: number = 0;
  private httpRequestsInProgress: number = 0;
  private startTime: number = Date.now();

  incrementHttpRequests(): void {
    this.httpRequestsTotal++;
  }

  incrementHttpRequestsByMethod(method: string): void {
    const current = this.httpRequestsByMethod.get(method) || 0;
    this.httpRequestsByMethod.set(method, current + 1);
  }

  incrementHttpRequestsByStatus(status: number): void {
    const current = this.httpRequestsByStatus.get(status) || 0;
    this.httpRequestsByStatus.set(status, current + 1);
  }

  incrementHttpRequestsByRoute(route: string): void {
    const current = this.httpRequestsByRoute.get(route) || 0;
    this.httpRequestsByRoute.set(route, current + 1);
  }

  addRequestDuration(duration: number): void {
    this.httpRequestDurationSum += duration;
    this.httpRequestDurationCount++;
  }

  incrementRequestsInProgress(): void {
    this.httpRequestsInProgress++;
  }

  decrementRequestsInProgress(): void {
    this.httpRequestsInProgress--;
  }

  getHttpRequestsTotal(): number {
    return this.httpRequestsTotal;
  }

  getHttpRequestsByMethod(): Map<string, number> {
    return this.httpRequestsByMethod;
  }

  getHttpRequestsByStatus(): Map<number, number> {
    return this.httpRequestsByStatus;
  }

  getHttpRequestsByRoute(): Map<string, number> {
    return this.httpRequestsByRoute;
  }

  getAverageRequestDuration(): number {
    return this.httpRequestDurationCount > 0
      ? this.httpRequestDurationSum / this.httpRequestDurationCount
      : 0;
  }

  getHttpRequestsInProgress(): number {
    return this.httpRequestsInProgress;
  }

  getUptimeSeconds(): number {
    return Math.floor((Date.now() - this.startTime) / 1000);
  }

  reset(): void {
    this.httpRequestsTotal = 0;
    this.httpRequestsByMethod.clear();
    this.httpRequestsByStatus.clear();
    this.httpRequestsByRoute.clear();
    this.httpRequestDurationSum = 0;
    this.httpRequestDurationCount = 0;
    this.httpRequestsInProgress = 0;
    this.startTime = Date.now();
  }
}

// Global metrics instance
const metrics = new MetricsStore();

/**
 * Middleware to collect HTTP request metrics - onRequest hook
 * This hook runs at the start of every request
 */
export const metricsMiddleware = async (
  request: FastifyRequest,
  reply: FastifyReply
): Promise<void> => {
  // Skip counting requests to the metrics endpoint itself to avoid infinite counting
  if (request.url !== '/metrics') {
    // Store start time for duration calculation
    (request as any).startTime = Date.now();

    // Increment total requests
    metrics.incrementHttpRequests();

    // Track requests by method
    metrics.incrementHttpRequestsByMethod(request.method);

    // Track requests by route (normalize dynamic routes)
    const normalizedRoute = normalizeRoute(request.url);
    metrics.incrementHttpRequestsByRoute(normalizedRoute);

    // Track requests in progress
    metrics.incrementRequestsInProgress();
  }
};

/**
 * Middleware to collect HTTP response metrics - onResponse hook
 * This hook runs after the response is sent
 */
export const metricsResponseMiddleware = async (
  request: FastifyRequest,
  reply: FastifyReply
): Promise<void> => {
  // Skip counting requests to the metrics endpoint itself
  if (request.url !== '/metrics') {
    const startTime = (request as any).startTime;
    if (startTime) {
      const duration = Date.now() - startTime;

      // Track response time
      metrics.addRequestDuration(duration);
    }

    // Track requests by status code
    metrics.incrementHttpRequestsByStatus(reply.statusCode);

    // Decrement requests in progress
    metrics.decrementRequestsInProgress();
  }
};

/**
 * Normalize route paths to group similar routes together
 * e.g., /api/users/123 -> /api/users/:id
 */
function normalizeRoute(url: string): string {
  // Remove query parameters
  const path = url.split('?')[0];

  // Common patterns to normalize
  return path
    .replace(/\/\d+/g, '/:id') // Replace numeric IDs
    .replace(/\/[a-f0-9-]{36}/g, '/:uuid') // Replace UUIDs
    .replace(/\/[a-f0-9]{24}/g, '/:objectId'); // Replace MongoDB ObjectIds
}

/**
 * Generate Prometheus-compatible metrics output
 * Returns metrics in the standard Prometheus text format
 */
export const getMetrics = (): string => {
  const lines: string[] = [];

  // Total HTTP requests
  lines.push('# HELP http_requests_total Total number of HTTP requests');
  lines.push('# TYPE http_requests_total counter');
  lines.push(`http_requests_total ${metrics.getHttpRequestsTotal()}`);
  lines.push('');

  // HTTP requests by method
  lines.push(
    '# HELP http_requests_by_method_total Total number of HTTP requests by method'
  );
  lines.push('# TYPE http_requests_by_method_total counter');
  for (const [method, count] of metrics.getHttpRequestsByMethod()) {
    lines.push(`http_requests_by_method_total{method="${method}"} ${count}`);
  }
  lines.push('');

  // HTTP requests by status code
  lines.push(
    '# HELP http_requests_by_status_total Total number of HTTP requests by status code'
  );
  lines.push('# TYPE http_requests_by_status_total counter');
  for (const [status, count] of metrics.getHttpRequestsByStatus()) {
    lines.push(`http_requests_by_status_total{status="${status}"} ${count}`);
  }
  lines.push('');

  // HTTP requests by route
  lines.push(
    '# HELP http_requests_by_route_total Total number of HTTP requests by route'
  );
  lines.push('# TYPE http_requests_by_route_total counter');
  for (const [route, count] of metrics.getHttpRequestsByRoute()) {
    lines.push(`http_requests_by_route_total{route="${route}"} ${count}`);
  }
  lines.push('');

  // Average request duration
  lines.push(
    '# HELP http_request_duration_seconds_avg Average HTTP request duration in seconds'
  );
  lines.push('# TYPE http_request_duration_seconds_avg gauge');
  lines.push(
    `http_request_duration_seconds_avg ${(metrics.getAverageRequestDuration() / 1000).toFixed(6)}`
  );
  lines.push('');

  // Requests currently in progress
  lines.push(
    '# HELP http_requests_in_progress Current number of HTTP requests being processed'
  );
  lines.push('# TYPE http_requests_in_progress gauge');
  lines.push(
    `http_requests_in_progress ${metrics.getHttpRequestsInProgress()}`
  );
  lines.push('');

  // Application uptime
  lines.push(
    '# HELP process_uptime_seconds Total uptime of the process in seconds'
  );
  lines.push('# TYPE process_uptime_seconds counter');
  lines.push(`process_uptime_seconds ${metrics.getUptimeSeconds()}`);
  lines.push('');

  // Node.js process metrics
  const memUsage = process.memoryUsage();
  lines.push('# HELP nodejs_memory_usage_bytes Node.js memory usage in bytes');
  lines.push('# TYPE nodejs_memory_usage_bytes gauge');
  lines.push(`nodejs_memory_usage_bytes{type="rss"} ${memUsage.rss}`);
  lines.push(
    `nodejs_memory_usage_bytes{type="heapTotal"} ${memUsage.heapTotal}`
  );
  lines.push(`nodejs_memory_usage_bytes{type="heapUsed"} ${memUsage.heapUsed}`);
  lines.push(`nodejs_memory_usage_bytes{type="external"} ${memUsage.external}`);
  lines.push('');

  return lines.join('\n');
};

/**
 * Reset metrics (useful for testing)
 */
export const resetMetrics = (): void => {
  metrics.reset();
};

/**
 * Get current metrics values (useful for testing)
 */
export const getCurrentMetrics = () => {
  return {
    httpRequestsTotal: metrics.getHttpRequestsTotal(),
  };
};
