# Prometheus configuration file
# This file configures Prometheus to scrape metrics from the Fastify application

global:
  scrape_interval: 15s # Set the scrape interval to every 15 seconds. Default is every 1 minute.
  evaluation_interval: 15s # Evaluate rules every 15 seconds. The default is every 1 minute.

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

# Load rules once and periodically evaluate them according to the global 'evaluation_interval'.
rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

# A scrape configuration containing exactly one endpoint to scrape:
scrape_configs:
  # The job name is added as a label `job=<job_name>` to any timeseries scraped from this config.
  - job_name: 'fastify-app'
    
    # Override the global default and scrape targets from this job every 5 seconds.
    scrape_interval: 5s
    
    # Metrics path defaults to '/metrics'
    metrics_path: /metrics
    
    # Scheme defaults to 'http'.
    scheme: http
    
    static_configs:
      # If running the app locally (not in Docker)
      - targets: ['localhost:3000']
      
      # If running the app in Docker and Prometheus outside Docker
      # - targets: ['host.docker.internal:3000']
      
      # If both app and Prometheus are in the same Docker network
      # - targets: ['fastify-app:3000']  # where 'fastify-app' is the container name
    
    # Optional: Add custom labels to all metrics from this job
    labels:
      environment: 'development'
      service: 'fastify-ms-boilerplate'

  # Example: Self-monitoring Prometheus
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
