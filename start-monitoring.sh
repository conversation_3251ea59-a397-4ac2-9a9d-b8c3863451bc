#!/bin/bash

# Script para inicializar o ambiente completo de monitoramento
# Fastify + Prometheus + Grafana + Node Exporter + cAdvisor

set -e

echo "🚀 Iniciando ambiente de monitoramento Fastify..."
echo ""

# Verificar se Docker está rodando
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker não está rodando. Por favor, inicie o Docker primeiro."
    exit 1
fi

# Verificar se Docker Compose está disponível
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose não encontrado. Por favor, instale o Docker Compose."
    exit 1
fi

# Criar diretórios necessários
echo "📁 Criando estrutura de diretórios..."
mkdir -p monitoring/grafana/provisioning/datasources
mkdir -p monitoring/grafana/provisioning/dashboards
mkdir -p monitoring/grafana/dashboards
mkdir -p monitoring/rules

# Verificar se os arquivos de configuração existem
echo "🔍 Verificando arquivos de configuração..."

required_files=(
    "monitoring/prometheus.yml"
    "monitoring/grafana/provisioning/datasources/prometheus.yml"
    "monitoring/grafana/provisioning/dashboards/dashboards.yml"
    "monitoring/grafana/dashboards/fastify-application.json"
    "docker-compose.monitoring.yml"
)

missing_files=()
for file in "${required_files[@]}"; do
    if [[ ! -f "$file" ]]; then
        missing_files+=("$file")
    fi
done

if [[ ${#missing_files[@]} -gt 0 ]]; then
    echo "❌ Arquivos de configuração faltando:"
    printf '   - %s\n' "${missing_files[@]}"
    echo ""
    echo "Por favor, certifique-se de que todos os arquivos de configuração foram criados."
    exit 1
fi

echo "✅ Todos os arquivos de configuração encontrados!"
echo ""

# Parar containers existentes (se houver)
echo "🛑 Parando containers existentes..."
docker-compose -f docker-compose.monitoring.yml down --remove-orphans 2>/dev/null || true

# Construir a aplicação Fastify
echo "🔨 Construindo aplicação Fastify..."
if [[ -f "package.json" ]]; then
    if command -v pnpm &> /dev/null; then
        pnpm install
        pnpm run build
    elif command -v npm &> /dev/null; then
        npm install
        npm run build
    else
        echo "❌ Nem pnpm nem npm encontrados. Por favor, instale um gerenciador de pacotes Node.js."
        exit 1
    fi
else
    echo "⚠️  package.json não encontrado. Pulando build da aplicação."
fi

# Iniciar todos os serviços
echo "🚀 Iniciando serviços de monitoramento..."
docker-compose -f docker-compose.monitoring.yml up -d

# Aguardar serviços ficarem prontos
echo "⏳ Aguardando serviços ficarem prontos..."
sleep 10

# Verificar status dos serviços
echo "🔍 Verificando status dos serviços..."
echo ""

services=("fastify-metrics-app" "prometheus" "grafana" "node-exporter" "cadvisor")
all_healthy=true

for service in "${services[@]}"; do
    if docker ps --format "table {{.Names}}\t{{.Status}}" | grep -q "$service.*Up"; then
        echo "✅ $service: Rodando"
    else
        echo "❌ $service: Não está rodando"
        all_healthy=false
    fi
done

echo ""

if $all_healthy; then
    echo "🎉 Todos os serviços estão rodando com sucesso!"
    echo ""
    echo "📊 URLs dos serviços:"
    echo "   🚀 Aplicação Fastify:  http://localhost:3000"
    echo "   📈 Métricas (/metrics): http://localhost:3000/metrics"
    echo "   🔍 Prometheus:         http://localhost:9090"
    echo "   📊 Grafana:            http://localhost:3001"
    echo "   🖥️  Node Exporter:      http://localhost:9100"
    echo "   🐳 cAdvisor:           http://localhost:8080"
    echo ""
    echo "🔐 Credenciais do Grafana:"
    echo "   Usuário: admin"
    echo "   Senha:   admin123"
    echo ""
    echo "💡 Dicas:"
    echo "   - O dashboard da aplicação Fastify já está configurado automaticamente"
    echo "   - Execute './test-metrics.sh' para gerar dados de teste"
    echo "   - Use 'docker-compose -f docker-compose.monitoring.yml logs -f' para ver logs"
    echo "   - Para parar: 'docker-compose -f docker-compose.monitoring.yml down'"
else
    echo "❌ Alguns serviços falharam ao iniciar. Verifique os logs:"
    echo "   docker-compose -f docker-compose.monitoring.yml logs"
    exit 1
fi
