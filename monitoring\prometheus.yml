# Prometheus configuration for Fastify monitoring
global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'fastify-monitor'

# Rules for alerting
rule_files:
  - "rules/*.yml"

# Alertmanager configuration (opcional)
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

# Scrape configurations
scrape_configs:
  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Fastify application metrics
  - job_name: 'fastify-app'
    scrape_interval: 5s
    metrics_path: /metrics
    static_configs:
      - targets: ['fastify-app:3000']
    scrape_timeout: 10s
    honor_labels: true
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: 'fastify-app'

  # Node Exporter (system metrics)
  - job_name: 'node-exporter'
    scrape_interval: 15s
    static_configs:
      - targets: ['node-exporter:9100']
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: 'docker-host'

  # cAdvisor (container metrics)
  - job_name: 'cadvisor'
    scrape_interval: 15s
    static_configs:
      - targets: ['cadvisor:8080']
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: 'docker-containers'

  # Health check endpoint
  - job_name: 'fastify-health'
    scrape_interval: 30s
    metrics_path: /health
    static_configs:
      - targets: ['fastify-app:3000']
    metric_relabel_configs:
      - source_labels: [__name__]
        regex: 'up'
        target_label: __name__
        replacement: 'fastify_health_up'
