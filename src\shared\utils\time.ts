/**
 * Native implementation to convert time strings to milliseconds
 * Replaces the 'ms' library with a simple native solution
 * 
 * Supported formats:
 * - s, sec, secs, second, seconds
 * - m, min, mins, minute, minutes  
 * - h, hr, hrs, hour, hours
 * - d, day, days
 * - w, week, weeks
 * - y, year, years
 */

const timeUnits: Record<string, number> = {
  // Seconds
  s: 1000,
  sec: 1000,
  secs: 1000,
  second: 1000,
  seconds: 1000,
  
  // Minutes
  m: 60 * 1000,
  min: 60 * 1000,
  mins: 60 * 1000,
  minute: 60 * 1000,
  minutes: 60 * 1000,
  
  // Hours
  h: 60 * 60 * 1000,
  hr: 60 * 60 * 1000,
  hrs: 60 * 60 * 1000,
  hour: 60 * 60 * 1000,
  hours: 60 * 60 * 1000,
  
  // Days
  d: 24 * 60 * 60 * 1000,
  day: 24 * 60 * 60 * 1000,
  days: 24 * 60 * 60 * 1000,
  
  // Weeks
  w: 7 * 24 * 60 * 60 * 1000,
  week: 7 * 24 * 60 * 60 * 1000,
  weeks: 7 * 24 * 60 * 60 * 1000,
  
  // Years (approximate)
  y: 365 * 24 * 60 * 60 * 1000,
  year: 365 * 24 * 60 * 60 * 1000,
  years: 365 * 24 * 60 * 60 * 1000,
};

/**
 * Convert a time string to milliseconds
 * @param timeString - Time string like "1h", "30m", "7d", etc.
 * @returns Number of milliseconds
 * @throws Error if the format is invalid
 */
export function parseTimeToMs(timeString: string): number {
  if (typeof timeString !== 'string') {
    throw new Error('Time string must be a string');
  }

  // Remove whitespace
  const str = timeString.trim().toLowerCase();
  
  // If it's already a number, assume it's milliseconds
  if (/^\d+$/.test(str)) {
    return parseInt(str, 10);
  }

  // Match pattern: number + unit (e.g., "1h", "30min", "7days")
  const match = str.match(/^(\d+(?:\.\d+)?)\s*([a-z]+)$/);
  
  if (!match) {
    throw new Error(`Invalid time format: ${timeString}. Expected format like "1h", "30m", "7d"`);
  }

  const [, valueStr, unit] = match;
  const value = parseFloat(valueStr);
  
  if (isNaN(value) || value < 0) {
    throw new Error(`Invalid time value: ${valueStr}`);
  }

  const multiplier = timeUnits[unit];
  
  if (!multiplier) {
    const validUnits = Object.keys(timeUnits).join(', ');
    throw new Error(`Invalid time unit: ${unit}. Valid units: ${validUnits}`);
  }

  return Math.round(value * multiplier);
}

/**
 * Alias for parseTimeToMs to match the 'ms' library API
 */
export const ms = parseTimeToMs;
