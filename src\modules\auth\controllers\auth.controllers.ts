import { validateBody } from '@/shared/validators/schema-validator';
import { ms } from '@/shared/utils/time';
import * as schemas from '../zod/auth.schema';
import * as authService from '../service/auth.service';
import { HttpStatus } from '@/shared/enums/status-code';
import { env } from '@/shared/config/env';
import type { FastifyRequest, FastifyReply } from 'fastify';
import { unsignCookie } from '@/shared/utils/cookies';

export const login = async (request: FastifyRequest, reply: FastifyReply) => {
  const { email, username, password } = validateBody(request).with(
    schemas.loginSchema
  );

  const responseData = await authService.login({
    email,
    username,
    password,
  });

  unsignCookie(request, reply, 'token');
  unsignCookie(request, reply, 'refreshToken');

  reply
    .setCookie('token', responseData.token, {
      httpOnly: true,
      secure: true,
      expires: new Date(Date.now() + ms(env.JWT_EXPIRES_IN)),
    })
    .setCookie('refreshToken', responseData.refreshToken, {
      httpOnly: true,
      secure: true,
      expires: new Date(Date.now() + ms(env.JWT_REFRESH_EXPIRES_IN)),
    })
    .status(HttpStatus.SUCCESS)
    .send({
      success: true,
      message: 'Login successful',
      ...responseData,
    });
};

export const refreshToken = async (
  request: FastifyRequest,
  reply: FastifyReply
) => {
  const tokenCookie = request.cookies['token'];
  const refreshTokenCookie = request.cookies['refreshToken'];

  const { refreshToken } = validateBody(request).with(
    schemas.refreshTokenSchema
  );

  const responseData = await authService.refreshToken({
    tokenCookie,
    refreshTokenCookie,
    refreshToken,
  });

  unsignCookie(request, reply, 'token');
  unsignCookie(request, reply, 'refreshToken');

  reply
    .setCookie('token', responseData.token, {
      httpOnly: true,
      secure: true,
      expires: new Date(Date.now() + ms(env.JWT_EXPIRES_IN)),
    })
    .setCookie('refreshToken', responseData.refreshToken, {
      httpOnly: true,
      secure: true,
      expires: new Date(Date.now() + ms(env.JWT_REFRESH_EXPIRES_IN)),
    })
    .status(HttpStatus.SUCCESS)
    .send({
      success: true,
      message: 'Token refreshed successfully',
      ...responseData,
    });
};

export const logout = async (request: FastifyRequest, reply: FastifyReply) => {
  const { token, refreshToken } = validateBody(request).with(
    schemas.logoutSchema
  );

  const responseData = await authService.logout({
    tokenCookie: request.cookies['token'],
    refreshTokenCookie: request.cookies['refreshToken'],
    token,
    refreshToken,
  });

  unsignCookie(request, reply, 'token');
  unsignCookie(request, reply, 'refreshToken');

  reply
    .clearCookie('token')
    .clearCookie('refreshToken')
    .status(HttpStatus.SUCCESS)
    .send({
      ...responseData,
    });
};
