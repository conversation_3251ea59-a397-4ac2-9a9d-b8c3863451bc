{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 1, "id": null, "links": [{"icon": "external link", "tags": [], "targetBlank": true, "title": "Application Health", "type": "link", "url": "http://localhost:3000/health"}], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 100, "panels": [], "title": "📊 Application Overview", "type": "row"}, {"datasource": "Prometheus", "description": "Taxa de requisições HTTP por segundo", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 2, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 50}, {"color": "red", "value": 100}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 4, "w": 4, "x": 0, "y": 1}, "id": 1, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "targets": [{"expr": "rate(http_requests_total[5m])", "interval": "", "legendFormat": "RPS", "refId": "A"}], "title": "Request Rate", "type": "stat"}, {"datasource": "Prometheus", "description": "Tempo médio de resposta em segundos", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 3, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 0.1}, {"color": "red", "value": 0.5}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 4, "w": 4, "x": 4, "y": 1}, "id": 2, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "targets": [{"expr": "http_request_duration_seconds_avg", "interval": "", "legendFormat": "Avg Response Time", "refId": "A"}], "title": "Avg Response Time", "type": "stat"}, {"datasource": "Prometheus", "description": "Requisições sendo processadas no momento", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 10}, {"color": "red", "value": 50}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 4, "w": 4, "x": 8, "y": 1}, "id": 3, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "targets": [{"expr": "http_requests_in_progress", "interval": "", "legendFormat": "In Progress", "refId": "A"}], "title": "Requests In Progress", "type": "stat"}, {"datasource": "Prometheus", "description": "Total de requisições processadas", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 4, "w": 4, "x": 12, "y": 1}, "id": 4, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "targets": [{"expr": "http_requests_total", "interval": "", "legendFormat": "Total", "refId": "A"}], "title": "Total Requests", "type": "stat"}, {"datasource": "Prometheus", "description": "Tempo de execução da aplicação", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 4, "w": 4, "x": 16, "y": 1}, "id": 5, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "targets": [{"expr": "process_uptime_seconds", "interval": "", "legendFormat": "Uptime", "refId": "A"}], "title": "Application Uptime", "type": "stat"}, {"datasource": "Prometheus", "description": "Uso de memória heap do Node.js", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 100000000}, {"color": "red", "value": 500000000}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 4, "w": 4, "x": 20, "y": 1}, "id": 6, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "targets": [{"expr": "nodejs_memory_usage_bytes{type=\"heapUsed\"}", "interval": "", "legendFormat": "Heap Used", "refId": "A"}], "title": "Memory Usage", "type": "stat"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 5}, "id": 200, "panels": [], "title": "📈 HTTP Metrics Details", "type": "row"}, {"datasource": "Prometheus", "description": "Taxa de requisições HTTP por segundo ao longo do tempo", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 6}, "id": 7, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"expr": "rate(http_requests_total[5m])", "interval": "", "legendFormat": "Request Rate", "refId": "A"}], "title": "HTTP Request Rate Over Time", "type": "timeseries"}, {"datasource": "Prometheus", "description": "Requisições HTTP por método (GET, POST, etc.)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 6}, "id": 8, "options": {"legend": {"calcs": ["min", "mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"expr": "rate(http_requests_by_method_total[5m])", "interval": "", "legendFormat": "{{method}}", "refId": "A"}], "title": "HTTP Requests by Method", "type": "timeseries"}], "schemaVersion": 27, "style": "dark", "tags": ["fastify", "nodejs", "application"], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "🚀 Fastify Application Dashboard", "uid": "fastify-app", "version": 1}