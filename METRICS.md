# Prometheus Metrics Integration

Este projeto inclui um sistema de métricas nativo compatível com Prometheus, implementado sem bibliotecas externas.

## 📊 Métricas Disponíveis

### `http_requests_total`
- **Tipo**: Counter
- **Descrição**: Número total de requisições HTTP recebidas
- **Formato**: `http_requests_total <valor>`

### `http_requests_by_method_total`
- **Tipo**: Counter
- **Descrição**: Número total de requisições HTTP por método HTTP
- **Labels**: `method` (GET, POST, PUT, DELETE, etc.)
- **Formato**: `http_requests_by_method_total{method="GET"} <valor>`

### `http_requests_by_status_total`
- **Tipo**: Counter
- **Descrição**: Número total de requisições HTTP por código de status
- **Labels**: `status` (200, 404, 500, etc.)
- **Formato**: `http_requests_by_status_total{status="200"} <valor>`

### `http_requests_by_route_total`
- **Tipo**: Counter
- **Descrição**: Número total de requisições HTTP por rota (normalizada)
- **Labels**: `route` (rotas normalizadas como `/api/users/:id`)
- **Formato**: `http_requests_by_route_total{route="/api/users"} <valor>`

### `http_request_duration_seconds_avg`
- **Tipo**: Gauge
- **Descrição**: Tempo médio de resposta das requisições HTTP em segundos
- **Formato**: `http_request_duration_seconds_avg <valor_em_segundos>`

### `http_requests_in_progress`
- **Tipo**: Gauge
- **Descrição**: Número atual de requisições HTTP sendo processadas
- **Formato**: `http_requests_in_progress <valor>`

### `process_uptime_seconds`
- **Tipo**: Counter
- **Descrição**: Tempo total de execução do processo em segundos
- **Formato**: `process_uptime_seconds <valor>`

### `nodejs_memory_usage_bytes`
- **Tipo**: Gauge
- **Descrição**: Uso de memória do processo Node.js em bytes
- **Labels**: `type` (rss, heapTotal, heapUsed, external)
- **Formato**: `nodejs_memory_usage_bytes{type="heapUsed"} <valor>`

## 🚀 Como Usar

### 1. Executar a Aplicação

```bash
# Desenvolvimento
pnpm run dev

# Produção
pnpm run build
pnpm start

# Docker
docker build -t fastify-metrics .
docker run -p 3000:3000 fastify-metrics
```

### 2. Acessar as Métricas

```bash
# Fazer algumas requisições para gerar métricas
curl http://localhost:3000/health
curl http://localhost:3000/api/users
curl http://localhost:3000/api/auth/login

# Visualizar as métricas
curl http://localhost:3000/metrics
```

**Exemplo de saída:**
```
# HELP http_requests_total Total number of HTTP requests
# TYPE http_requests_total counter
http_requests_total 15

# HELP http_requests_by_method_total Total number of HTTP requests by method
# TYPE http_requests_by_method_total counter
http_requests_by_method_total{method="GET"} 12
http_requests_by_method_total{method="POST"} 3

# HELP http_requests_by_status_total Total number of HTTP requests by status code
# TYPE http_requests_by_status_total counter
http_requests_by_status_total{status="200"} 13
http_requests_by_status_total{status="404"} 2

# HELP http_requests_by_route_total Total number of HTTP requests by route
# TYPE http_requests_by_route_total counter
http_requests_by_route_total{route="/health"} 8
http_requests_by_route_total{route="/api/users"} 4
http_requests_by_route_total{route="/api/users/:id"} 3

# HELP http_request_duration_seconds_avg Average HTTP request duration in seconds
# TYPE http_request_duration_seconds_avg gauge
http_request_duration_seconds_avg 0.045123

# HELP http_requests_in_progress Current number of HTTP requests being processed
# TYPE http_requests_in_progress gauge
http_requests_in_progress 0

# HELP process_uptime_seconds Total uptime of the process in seconds
# TYPE process_uptime_seconds counter
process_uptime_seconds 3600

# HELP nodejs_memory_usage_bytes Node.js memory usage in bytes
# TYPE nodejs_memory_usage_bytes gauge
nodejs_memory_usage_bytes{type="rss"} 52428800
nodejs_memory_usage_bytes{type="heapTotal"} 29360128
nodejs_memory_usage_bytes{type="heapUsed"} 18874496
nodejs_memory_usage_bytes{type="external"} 1089024
```

## 🐳 Configuração com Docker

### Dockerfile
O projeto inclui um Dockerfile otimizado que:
- Usa `node:18-alpine` como base
- Constrói a aplicação para produção
- Expõe a porta configurável via variável de ambiente
- Inclui health check
- Executa com usuário não-root para segurança

### Executar com Docker

```bash
# Build da imagem
docker build -t fastify-metrics .

# Executar o container
docker run -d \
  --name fastify-app \
  -p 3000:3000 \
  -e PORT=3000 \
  -e NODE_ENV=production \
  fastify-metrics

# Testar as métricas
curl http://localhost:3000/metrics
```

## 📈 Configuração do Prometheus

### 1. Usar o arquivo prometheus.yml incluído

O projeto inclui um arquivo `prometheus.yml` de exemplo com configurações para diferentes cenários:

```yaml
scrape_configs:
  - job_name: 'fastify-app'
    scrape_interval: 5s
    static_configs:
      # Aplicação local
      - targets: ['localhost:3000']
      
      # Aplicação em Docker (Prometheus fora do Docker)
      # - targets: ['host.docker.internal:3000']
      
      # Ambos em Docker na mesma rede
      # - targets: ['fastify-app:3000']
```

### 2. Executar o Prometheus

```bash
# Download do Prometheus
wget https://github.com/prometheus/prometheus/releases/download/v2.45.0/prometheus-2.45.0.linux-amd64.tar.gz
tar xvfz prometheus-*.tar.gz
cd prometheus-*

# Copiar o arquivo de configuração
cp /path/to/your/project/prometheus.yml .

# Executar o Prometheus
./prometheus --config.file=prometheus.yml
```

### 3. Acessar o Prometheus

- **Prometheus UI**: http://localhost:9090
- **Query de exemplo**: `http_requests_total`

## 🔧 Configuração Avançada

### Variáveis de Ambiente

```bash
# Porta da aplicação (padrão: 3000)
PORT=3000

# Ambiente (development/production)
NODE_ENV=production
```

### Docker Compose com Prometheus

```yaml
version: '3.8'
services:
  fastify-app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000

  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
```

## 🧪 Testando as Métricas

```bash
# Script para gerar tráfego
for i in {1..10}; do
  curl -s http://localhost:3000/health > /dev/null
  curl -s http://localhost:3000/api/users > /dev/null
done

# Verificar as métricas
curl http://localhost:3000/metrics
```

## 📝 Implementação Técnica

### Middleware de Métricas
- Implementado em `src/shared/middleware/metrics.middleware.ts`
- Usa um contador em memória simples
- Incrementa a cada requisição HTTP (exceto `/metrics`)
- Thread-safe para aplicações single-threaded do Node.js

### Endpoint /metrics
- Retorna dados no formato Prometheus padrão
- Content-Type: `text/plain`
- Inclui metadados HELP e TYPE
- Compatível com scrapers Prometheus

### Características
- ✅ Zero dependências externas
- ✅ Formato Prometheus nativo
- ✅ Configurável via variáveis de ambiente
- ✅ Docker-ready
- ✅ Health check incluído
- ✅ Segurança (usuário não-root)
