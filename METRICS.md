# Prometheus Metrics Integration

Este projeto inclui um sistema de métricas nativo compatível com Prometheus, implementado sem bibliotecas externas.

## 📊 Métricas Disponíveis

### `http_requests_total`
- **Tipo**: Counter
- **Des<PERSON>rição**: Número total de requisições HTTP recebidas
- **Formato**: `http_requests_total <valor>`

## 🚀 Como Usar

### 1. Executar a Aplicação

```bash
# Desenvolvimento
pnpm run dev

# Produção
pnpm run build
pnpm start

# Docker
docker build -t fastify-metrics .
docker run -p 3000:3000 fastify-metrics
```

### 2. <PERSON><PERSON><PERSON> as Métricas

```bash
# Fazer algumas requisições para gerar métricas
curl http://localhost:3000/health
curl http://localhost:3000/api/users
curl http://localhost:3000/api/auth/login

# Visualizar as métricas
curl http://localhost:3000/metrics
```

**Exemplo de saída:**
```
# HELP http_requests_total Total number of HTTP requests
# TYPE http_requests_total counter
http_requests_total 15
```

## 🐳 Configuração com Docker

### Dockerfile
O projeto inclui um Dockerfile otimizado que:
- Usa `node:18-alpine` como base
- Constrói a aplicação para produção
- Expõe a porta configurável via variável de ambiente
- Inclui health check
- Executa com usuário não-root para segurança

### Executar com Docker

```bash
# Build da imagem
docker build -t fastify-metrics .

# Executar o container
docker run -d \
  --name fastify-app \
  -p 3000:3000 \
  -e PORT=3000 \
  -e NODE_ENV=production \
  fastify-metrics

# Testar as métricas
curl http://localhost:3000/metrics
```

## 📈 Configuração do Prometheus

### 1. Usar o arquivo prometheus.yml incluído

O projeto inclui um arquivo `prometheus.yml` de exemplo com configurações para diferentes cenários:

```yaml
scrape_configs:
  - job_name: 'fastify-app'
    scrape_interval: 5s
    static_configs:
      # Aplicação local
      - targets: ['localhost:3000']
      
      # Aplicação em Docker (Prometheus fora do Docker)
      # - targets: ['host.docker.internal:3000']
      
      # Ambos em Docker na mesma rede
      # - targets: ['fastify-app:3000']
```

### 2. Executar o Prometheus

```bash
# Download do Prometheus
wget https://github.com/prometheus/prometheus/releases/download/v2.45.0/prometheus-2.45.0.linux-amd64.tar.gz
tar xvfz prometheus-*.tar.gz
cd prometheus-*

# Copiar o arquivo de configuração
cp /path/to/your/project/prometheus.yml .

# Executar o Prometheus
./prometheus --config.file=prometheus.yml
```

### 3. Acessar o Prometheus

- **Prometheus UI**: http://localhost:9090
- **Query de exemplo**: `http_requests_total`

## 🔧 Configuração Avançada

### Variáveis de Ambiente

```bash
# Porta da aplicação (padrão: 3000)
PORT=3000

# Ambiente (development/production)
NODE_ENV=production
```

### Docker Compose com Prometheus

```yaml
version: '3.8'
services:
  fastify-app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000

  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
```

## 🧪 Testando as Métricas

```bash
# Script para gerar tráfego
for i in {1..10}; do
  curl -s http://localhost:3000/health > /dev/null
  curl -s http://localhost:3000/api/users > /dev/null
done

# Verificar as métricas
curl http://localhost:3000/metrics
```

## 📝 Implementação Técnica

### Middleware de Métricas
- Implementado em `src/shared/middleware/metrics.middleware.ts`
- Usa um contador em memória simples
- Incrementa a cada requisição HTTP (exceto `/metrics`)
- Thread-safe para aplicações single-threaded do Node.js

### Endpoint /metrics
- Retorna dados no formato Prometheus padrão
- Content-Type: `text/plain`
- Inclui metadados HELP e TYPE
- Compatível com scrapers Prometheus

### Características
- ✅ Zero dependências externas
- ✅ Formato Prometheus nativo
- ✅ Configurável via variáveis de ambiente
- ✅ Docker-ready
- ✅ Health check incluído
- ✅ Segurança (usuário não-root)
