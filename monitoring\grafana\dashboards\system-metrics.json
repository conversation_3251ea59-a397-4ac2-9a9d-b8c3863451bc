{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": 1860, "graphTooltip": 1, "id": null, "links": [], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 100, "panels": [], "title": "🖥️ System Overview", "type": "row"}, {"datasource": "Prometheus", "description": "Porcentagem de uso da CPU", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 1, "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 70}, {"color": "red", "value": 90}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 1}, "id": 1, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "8.0.0", "targets": [{"expr": "100 - (avg(irate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)", "interval": "", "legendFormat": "CPU Usage", "refId": "A"}], "title": "CPU Usage", "type": "gauge"}, {"datasource": "Prometheus", "description": "Porcentagem de uso da memória RAM", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 1, "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 80}, {"color": "red", "value": 95}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 1}, "id": 2, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "8.0.0", "targets": [{"expr": "((node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes) * 100", "interval": "", "legendFormat": "Memory Usage", "refId": "A"}], "title": "Memory Usage", "type": "gauge"}, {"datasource": "Prometheus", "description": "Porcentagem de uso do disco", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 1, "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 80}, {"color": "red", "value": 95}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 1}, "id": 3, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "8.0.0", "targets": [{"expr": "((node_filesystem_size_bytes{fstype!=\"tmpfs\"} - node_filesystem_avail_bytes{fstype!=\"tmpfs\"}) / node_filesystem_size_bytes{fstype!=\"tmpfs\"}) * 100", "interval": "", "legendFormat": "{{mountpoint}}", "refId": "A"}], "title": "Disk Usage", "type": "gauge"}, {"datasource": "Prometheus", "description": "Uptime do sistema em dias", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 1, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "d"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 1}, "id": 4, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.0.0", "targets": [{"expr": "node_time_seconds - node_boot_time_seconds", "interval": "", "legendFormat": "Uptime", "refId": "A"}], "title": "System Uptime", "type": "stat", "transformations": [{"id": "calculateField", "options": {"mode": "reduceRow", "reduce": {"include": ["Value"], "reducer": "lastNotNull"}, "replaceFields": false}}]}], "schemaVersion": 27, "style": "dark", "tags": ["system", "node-exporter", "infrastructure"], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "🖥️ System Metrics Dashboard", "uid": "system-metrics", "version": 1}