#!/bin/bash

# Script para testar as métricas do Fastify
# Este script faz várias requisições para gerar dados de métricas

BASE_URL="http://localhost:3000"

echo "🚀 Testando métricas do Fastify..."
echo "Base URL: $BASE_URL"
echo ""

# Função para fazer requisições
make_request() {
    local method=$1
    local endpoint=$2
    local expected_status=$3
    
    echo "📡 $method $endpoint"
    
    if [ "$method" = "GET" ]; then
        curl -s -w "Status: %{http_code}, Time: %{time_total}s\n" \
             -o /dev/null "$BASE_URL$endpoint"
    elif [ "$method" = "POST" ]; then
        curl -s -w "Status: %{http_code}, Time: %{time_total}s\n" \
             -X POST \
             -H "Content-Type: application/json" \
             -d '{"test": "data"}' \
             -o /dev/null "$BASE_URL$endpoint"
    fi
}

echo "1️⃣ Fazendo requisições para gerar métricas..."
echo ""

# Health checks
for i in {1..5}; do
    make_request "GET" "/health"
done

# API endpoints (alguns vão retornar 404, mas isso é esperado para testar métricas de erro)
make_request "GET" "/api/users"
make_request "GET" "/api/users/123"
make_request "GET" "/api/users/456"
make_request "POST" "/api/users"
make_request "GET" "/api/auth/login"
make_request "POST" "/api/auth/login"

# Endpoints que não existem (para gerar 404s)
make_request "GET" "/nonexistent"
make_request "GET" "/api/nonexistent"

echo ""
echo "2️⃣ Aguardando um momento para as métricas serem processadas..."
sleep 2

echo ""
echo "3️⃣ Coletando métricas..."
echo "📊 Métricas atuais:"
echo "===================="

curl -s "$BASE_URL/metrics"

echo ""
echo "===================="
echo "✅ Teste concluído!"
echo ""
echo "💡 Dicas:"
echo "- Execute este script várias vezes para ver as métricas aumentarem"
echo "- Acesse $BASE_URL/metrics diretamente no navegador"
echo "- Use 'watch curl -s $BASE_URL/metrics' para monitorar em tempo real"
