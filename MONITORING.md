# 📊 Guia de Monitoramento Fastify

Este guia explica como usar o sistema completo de monitoramento com Prometheus + Grafana para sua aplicação Fastify.

## 🏗️ Arquitetura

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Fastify App   │───▶│   Prometheus    │───▶│     Grafana     │
│  localhost:3000 │    │  localhost:9090 │    │  localhost:3001 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
   /metrics endpoint      Coleta métricas        Dashboards
   /health endpoint       a cada 5s              Visualizações
```

## 🚀 Como Usar

### 1. Iniciar a Aplicação Fastify
```bash
# Terminal 1: Sua aplicação de desenvolvimento
npm run dev
```
Sua aplicação ficará disponível em: http://localhost:3000

### 2. Iniciar o Ambiente de Monitoramento
```bash
# Terminal 2: Serviços de monitoramento
docker-compose -f docker-compose.monitoring.yml up -d
```

### 3. Acessar os Serviços

| Serviço | URL | Credenciais |
|---------|-----|-------------|
| 🚀 **Aplicação Fastify** | http://localhost:3000 | - |
| 📈 **Métricas** | http://localhost:3000/metrics | - |
| 🏥 **Health Check** | http://localhost:3000/health | - |
| 🔍 **Prometheus** | http://localhost:9090 | - |
| 📊 **Grafana** | http://localhost:3001 | admin/admin123 |
| 🖥️ **Node Exporter** | http://localhost:9100 | - |
| 🐳 **cAdvisor** | http://localhost:8080 | - |

## 📊 Dashboards Disponíveis

### 1. 🚀 Fastify Application Dashboard
- Taxa de requisições por segundo
- Tempo médio de resposta
- Requisições em progresso
- Total de requisições
- Uptime da aplicação
- Uso de memória

### 2. 🖥️ System Metrics Dashboard
- Uso de CPU do sistema
- Uso de memória RAM
- Uso de disco
- Uptime do sistema

### 3. 🐳 Container Metrics Dashboard
- Containers rodando
- Uso de CPU por container
- Uso de memória por container
- Tráfego de rede

## 🔧 Como Funciona

### Coleta de Métricas
1. **Fastify App** expõe métricas em `/metrics` no formato Prometheus
2. **Prometheus** faz scrape das métricas a cada 5 segundos
3. **Grafana** consulta o Prometheus e exibe nos dashboards

### Métricas Coletadas
- `http_requests_total` - Total de requisições HTTP
- `http_requests_by_method_total` - Requisições por método (GET, POST, etc.)
- `http_requests_by_status_total` - Requisições por status (200, 404, 500, etc.)
- `http_requests_by_route_total` - Requisições por rota
- `http_request_duration_seconds_*` - Tempo de resposta (min, max, avg)
- `http_requests_in_progress` - Requisições sendo processadas
- `nodejs_memory_usage_bytes` - Uso de memória do Node.js
- `process_uptime_seconds` - Tempo de execução da aplicação

## 🧪 Testando as Métricas

Execute o script de teste para gerar dados:
```bash
./test-metrics.sh
```

Ou faça requisições manuais:
```bash
# Requisições normais
curl http://localhost:3000/health
curl http://localhost:3000/api/users

# Ver métricas
curl http://localhost:3000/metrics
```

## 🛠️ Comandos Úteis

```bash
# Iniciar monitoramento
docker-compose -f docker-compose.monitoring.yml up -d

# Ver logs
docker-compose -f docker-compose.monitoring.yml logs -f

# Parar monitoramento
docker-compose -f docker-compose.monitoring.yml down

# Reiniciar apenas o Grafana
docker-compose -f docker-compose.monitoring.yml restart grafana

# Ver status dos containers
docker-compose -f docker-compose.monitoring.yml ps
```

## 🎯 Vantagens desta Abordagem

✅ **Uma única aplicação**: Não duplica a aplicação em containers  
✅ **Desenvolvimento ágil**: Usa `npm run dev` com hot reload  
✅ **Métricas reais**: Coleta dados da aplicação que você está desenvolvendo  
✅ **Fácil debug**: Logs e erros aparecem diretamente no terminal  
✅ **Produção-ready**: Mesmas métricas funcionam em produção  

## 🔍 Troubleshooting

### Prometheus não consegue acessar a aplicação
- Verifique se a aplicação está rodando em `localhost:3000`
- No Windows/Mac, o Docker usa `host.docker.internal` para acessar o host

### Grafana não carrega dashboards
- Verifique se os arquivos estão em `monitoring/grafana/dashboards/`
- Reinicie o Grafana: `docker-compose -f docker-compose.monitoring.yml restart grafana`

### Métricas não aparecem
- Acesse http://localhost:3000/metrics para verificar se estão sendo geradas
- Verifique no Prometheus (http://localhost:9090) se o target está UP
